"""
Survey Metrics Calculator - Free Version

This module provides a comprehensive suite of functions to calculate various metrics
from company survey responses, including:
- DEI (Diversity, Equity, Inclusion) scores
- Department-wise metrics
- Experience-based metrics
- Role engagement metrics
- Employee voice metrics

Only considers questions [1, 2, 5, 16, 10, 15] for free version.
"""

from decimal import Decimal

# Constants for mapping form values to database fields
DEPARTMENT_MAPPING = {
    'HR' : 'HR_and_Admin',
    'Finance' : 'Finance_and_accounting',
    'Sales' : 'Sales_marketing',
    'Product' : 'Product_development',
    'Technical' : 'Technical',
    'Operations' : 'Operations',
    'Procurements' : 'Procurement',
    'Quality' : 'Quality',
    'Business' : 'Business_Development' ,
    'Executive': 'executive',
    'Leadership' : 'leadership',
    'Management' : 'Management',
    'Others' : 'Others'
}

EXPERIENCE_MAPPING = {
    '0-1 year': '1to2',  # Group with 1-2 years
    '1-3 years': '1to2',
    '3-5 years': '3to5',
    '5+ years': 'above5', 
}

ROLE_MAPPING = {
    'Junior Staff' : 'Junior_staff',
    'Senior Staff' : 'Senior_staff',
    'Manager': 'manager',
    'Executive': 'executive',
    'Trainee' : 'trainee',
    'Team' : 'team_member'
}

Gender_Mapping = {
    'Male' : 'Male',
    'Female' : 'Female',
    'Other' : 'Others'
}

# Question Category Mappings - Free Version (only questions [1, 2, 5, 16, 10, 15])
DIVERSITY_QUESTIONS = [1, 2]  # From [1, 2, 3, 13, 19]
EQUITY_QUESTIONS = [5, 16]  # From [4, 5, 6, 11, 16, 17, 18]
INCLUSION_QUESTIONS = [10, 15]  # From [7, 8, 9, 10, 12, 14, 15, 20]

CULTURE_OF_ENGAGEMENT_QUESTIONS = [10, 15]  # From [3, 7, 8, 9, 10, 15, 19, 20]
STRATEGIC_ALIGNMENT_QUESTIONS = []  # No questions from free set
SUPPORT_AND_MOTIVATION_QUESTIONS = [2, 5, 10]  # From [2, 4, 5, 6, 8, 9, 10, 13]
SKILL_DEVELOPMENT_QUESTIONS = [16]  # From [4, 16, 18]
ENGAGEMENT_RATE_QUESTIONS = [10, 15]  # From [3, 7, 8, 9, 10, 15, 19, 20]

CREDIBILITY_QUESTIONS = [5, 15]  # From [5, 8, 11, 13, 15]
FAIRNESS_QUESTIONS = [1, 2, 5, 16]  # From [1, 2, 5, 12, 13, 14, 16, 18]
WORKPLACE_SATISFACTION_QUESTIONS = [10, 16]  # From [7, 9, 10, 16, 17]
TEAM_SPIRIT_QUESTIONS = [15]  # From [3, 8, 9, 14, 15, 19, 20]
WELL_BEING_QUESTIONS = []  # No questions from free set
RESPECT_QUESTIONS = [2, 10]  # From [2, 3, 8, 9, 10, 12, 14, 18, 19]
OPEN_COMMUNICATION_QUESTIONS = []  # No questions from free set
RECOGNITION_QUESTIONS = [10, 16]  # From [3, 9, 10, 14, 16, 19]
MOTIVATION_QUESTIONS = [10, 16]  # From [4, 6, 10, 16, 18]
PROUD_TO_WORK_HERE_QUESTIONS = [15]  # From [11,15,20]
SAVES_PEOPLES_CARE_QUESTIONS = [2]  # From [2,7,9,12,17]
FAIR_PROMOTION_QUESTIONS = [1, 5]  # From [1,5,13]
INVOLVEMENT_DECISION_QUESTIONS = []  # No questions from free set
LEADERSHIP_REACHABLE_QUESTIONS = [15]  # From [8,11,13,14,15]

# LPW Categories (in 1-20 format) - Free Version
LEADERSHIP_QUESTIONS = []  # No questions from free set
POLICIES_QUESTIONS = [1, 2, 5, 15, 16]  # From [1, 2, 3, 4, 5, 6, 8, 11, 15, 16, 17]
WORKPLACE_CULTURE_QUESTIONS = []  # No questions from free set

# Employee Voice Statement Questions - Free Version
EMP_VOICE_QUESTIONS = {
    1: [],        # "I feel safe to speak up without fear of negative consequences."
    2: [],        # "Leaders actively listen and follow through on employee feedback"
    3: [],        # "The company consistently lives by its stated values"
    4: [],        # "Our internal culture aligns with what we promote externally"
    5: [],        # "Leadership communicates with transparency and honesty"
    6: [15],      # "I trust leaders to make fair and thoughtful decisions"
    7: [],        # "I feel a genuine sense of belonging in the organization"
    8: [2],       # "My unique identity and contributions are respected and valued"
    9: [10],      # "My work is recognized, and I feel appreciated"
    10: [],       # "I see clear opportunities for growth and development here"
    11: [],       # "I have a healthy work-life balance and feel supported"
    12: [],       # "The company genuinely cares about my mental and emotional well-being"
    13: [1, 5]    # "Promotions and rewards are handled fairly and transparently"
}

# Benchmarks for KPI sets
BENCHMARKS_1ST_KPI = {
    'credibility': 85,
    'fairness': 75,
    'workplace_satisfaction': 80,
    'team_spirit': 65,
    'well_being': 72,
    'respect': 67,
    'open_communication': 56
}

BENCHMARKS_2ND_KPI = {
    'recognition': 75,
    'motivation': 75
}

# Core calculation functions
def calculate_total_responses(survey_responses):
    """Calculate the total number of unique employees who responded to the survey."""
    return survey_responses['roll_no'].nunique()

def calculate_response_rate(total_responses, num_surveys):
    """Calculate the response rate as a percentage."""
    if num_surveys <= 0:
        return Decimal('0.00')
    
    response_rate = (Decimal(total_responses) / Decimal(num_surveys)) * 100
    return response_rate.quantize(Decimal('0.01'))

def calculate_sentiment_percentages(survey_responses):
    """Calculate overall sentiment percentages."""
    total_with_sentiment = survey_responses[survey_responses['predicted_sentiment'].notna()].shape[0]  
    
    if total_with_sentiment == 0:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')

    positive_count = survey_responses[survey_responses['predicted_sentiment'] == "Positive"].shape[0]  
    neutral_count = survey_responses[survey_responses['predicted_sentiment'] == "Neutral"].shape[0]  
    negative_count = survey_responses[survey_responses['predicted_sentiment'] == "Negative"].shape[0] 

    positive_pct = (Decimal(positive_count) / Decimal(total_with_sentiment)) * 100
    neutral_pct = (Decimal(neutral_count) / Decimal(total_with_sentiment)) * 100
    negative_pct = (Decimal(negative_count) / Decimal(total_with_sentiment)) * 100

    return (
        positive_pct.quantize(Decimal('0.01')),
        neutral_pct.quantize(Decimal('0.01')),
        negative_pct.quantize(Decimal('0.01'))
    )

def calculate_category_sentiment_percentages(survey_responses, question_numbers):
    """Calculate sentiment percentages for a specific category of questions."""
    category_responses = survey_responses[survey_responses['question_number'].isin(question_numbers)]
    return calculate_sentiment_percentages(category_responses)
    
def calculate_dei_score(positive_pct, neutral_pct, negative_pct):
    """Calculate DEI score based on sentiment percentages."""
    score = (
        (positive_pct * Decimal('1.0')) +
        (neutral_pct * Decimal('0.5')) +
        (negative_pct * Decimal('-0.1'))
    )
    return score.quantize(Decimal('0.01'))

# Department metrics
def calculate_department_metrics(survey_responses):
    """Calculate DEI metrics for each department."""
    metrics = {}
    
    for dept_form_value, dept_db_value in DEPARTMENT_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['department'] == dept_form_value]
        
        if filtered_responses.empty:
            metrics[f'dept_{dept_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'dept_{dept_db_value}_negative_pct'] = Decimal('0.00')
            continue
        
        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
        
        metrics[f'dept_{dept_db_value}_dei_score'] = dei_score
        metrics[f'dept_{dept_db_value}_positive_pct'] = positive_pct
        metrics[f'dept_{dept_db_value}_neutral_pct'] = neutral_pct
        metrics[f'dept_{dept_db_value}_negative_pct'] = negative_pct
    
    return metrics

# Experience metrics
def calculate_experience_metrics(survey_responses):
    """Calculate DEI metrics for different experience levels."""
    metrics = {}
    
    for exp_form_value, exp_db_value in EXPERIENCE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['experience'] == exp_form_value]
        
        if filtered_responses.empty:
            metrics[f'exp_{exp_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'exp_{exp_db_value}_negative_pct'] = Decimal('0.00')
            continue
        
        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)
        
        metrics[f'exp_{exp_db_value}_dei_score'] = dei_score
        metrics[f'exp_{exp_db_value}_positive_pct'] = positive_pct
        metrics[f'exp_{exp_db_value}_neutral_pct'] = neutral_pct
        metrics[f'exp_{exp_db_value}_negative_pct'] = negative_pct
    
    return metrics

# Role engagement metrics
def calculate_role_engagement_metrics(survey_responses):
    """Calculate engagement metrics for different roles."""
    metrics = {}

    for role_form_value, role_db_value in ROLE_MAPPING.items():
        filtered_responses = survey_responses[survey_responses['role'] == role_form_value] 

        if filtered_responses.empty:
            metrics[f'role_{role_db_value}_engagement_score'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'role_{role_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses,
            CULTURE_OF_ENGAGEMENT_QUESTIONS
        )
        engagement_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'role_{role_db_value}_engagement_score'] = engagement_score
        metrics[f'role_{role_db_value}_positive_pct'] = positive_pct
        metrics[f'role_{role_db_value}_neutral_pct'] = neutral_pct
        metrics[f'role_{role_db_value}_negative_pct'] = negative_pct

    return metrics

def calculate_all_metrics(survey_responses):
    """Calculate all metrics in a single pass and return them in a dictionary."""
    metrics = {}

    total_responses = calculate_total_responses(survey_responses)
    positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(survey_responses)
    metrics['overall'] = {
        'total_responses': total_responses,
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'dei_score': calculate_dei_score(positive_pct, neutral_pct, negative_pct)
    }
    metrics.update(calculate_all_category_metrics(survey_responses))
    metrics['departments'] = calculate_department_metrics(survey_responses)
    metrics['experiences'] = calculate_experience_metrics(survey_responses)
    metrics['roles'] = calculate_role_engagement_metrics(survey_responses)
    metrics['genders'] = calculate_gender_metrics(survey_responses)
    metrics['voice'] = calculate_all_voice_metrics(survey_responses)
    metrics['strategic_alignment_gender'] = calculate_strategic_alignment_gender_metrics(survey_responses)

    return metrics

def calculate_gender_metrics(survey_responses):
    """Calculate DEI metrics for different gender categories."""
    metrics = {}

    for gender_form_value, gender_db_value in Gender_Mapping.items():
        filtered_responses = survey_responses[survey_responses['gender'] == gender_form_value]

        if filtered_responses.empty:
            metrics[f'gender_{gender_db_value}_dei_score'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'gender_{gender_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(filtered_responses)
        dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'gender_{gender_db_value}_dei_score'] = dei_score
        metrics[f'gender_{gender_db_value}_positive_pct'] = positive_pct
        metrics[f'gender_{gender_db_value}_neutral_pct'] = neutral_pct
        metrics[f'gender_{gender_db_value}_negative_pct'] = negative_pct

    return metrics

def calculate_strategic_alignment_gender_metrics(survey_responses):
    """Calculate strategic alignment metrics by gender."""
    metrics = {}

    if not STRATEGIC_ALIGNMENT_QUESTIONS:
        for gender_form_value, gender_db_value in Gender_Mapping.items():
            metrics[f'strategic_alignment_gender_{gender_db_value}_score'] = Decimal('0.00')
            metrics[f'strategic_alignment_gender_{gender_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'strategic_alignment_gender_{gender_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'strategic_alignment_gender_{gender_db_value}_negative_pct'] = Decimal('0.00')
        return metrics

    for gender_form_value, gender_db_value in Gender_Mapping.items():
        filtered_responses = survey_responses[survey_responses['gender'] == gender_form_value]

        if filtered_responses.empty:
            metrics[f'strategic_alignment_gender_{gender_db_value}_score'] = Decimal('0.00')
            metrics[f'strategic_alignment_gender_{gender_db_value}_positive_pct'] = Decimal('0.00')
            metrics[f'strategic_alignment_gender_{gender_db_value}_neutral_pct'] = Decimal('0.00')
            metrics[f'strategic_alignment_gender_{gender_db_value}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            filtered_responses, STRATEGIC_ALIGNMENT_QUESTIONS
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        metrics[f'strategic_alignment_gender_{gender_db_value}_score'] = score
        metrics[f'strategic_alignment_gender_{gender_db_value}_positive_pct'] = positive_pct
        metrics[f'strategic_alignment_gender_{gender_db_value}_neutral_pct'] = neutral_pct
        metrics[f'strategic_alignment_gender_{gender_db_value}_negative_pct'] = negative_pct

    return metrics

def calculate_all_voice_metrics(survey_responses):
    """Calculate all employee voice metrics."""
    voice_metrics = {}

    for statement_num, question_list in EMP_VOICE_QUESTIONS.items():
        if not question_list:
            voice_metrics[f'voice_statement_{statement_num}_score'] = Decimal('0.00')
            voice_metrics[f'voice_statement_{statement_num}_positive_pct'] = Decimal('0.00')
            voice_metrics[f'voice_statement_{statement_num}_neutral_pct'] = Decimal('0.00')
            voice_metrics[f'voice_statement_{statement_num}_negative_pct'] = Decimal('0.00')
            continue

        positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
            survey_responses, question_list
        )
        score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

        voice_metrics[f'voice_statement_{statement_num}_score'] = score
        voice_metrics[f'voice_statement_{statement_num}_positive_pct'] = positive_pct
        voice_metrics[f'voice_statement_{statement_num}_neutral_pct'] = neutral_pct
        voice_metrics[f'voice_statement_{statement_num}_negative_pct'] = negative_pct

    return voice_metrics

def calculate_diversity_metrics(survey_responses):
    """Calculate diversity-related metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, DIVERSITY_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_equity_metrics(survey_responses):
    """Calculate equity-related metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, EQUITY_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_inclusion_metrics(survey_responses):
    """Calculate inclusion-related metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, INCLUSION_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_credibility_metrics(survey_responses):
    """Calculate credibility-related metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, CREDIBILITY_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['credibility']
    }

def calculate_fairness_metrics(survey_responses):
    """Calculate fairness-related metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, FAIRNESS_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['fairness']
    }

def calculate_workplace_satisfaction_metrics(survey_responses):
    """Calculate workplace satisfaction metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, WORKPLACE_SATISFACTION_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['workplace_satisfaction']
    }

def calculate_team_spirit_metrics(survey_responses):
    """Calculate team spirit metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, TEAM_SPIRIT_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['team_spirit']
    }

def calculate_wellbeing_metrics(survey_responses):
    """Calculate well-being metrics and return them in a dictionary."""
    if not WELL_BEING_QUESTIONS:
        return {
            'positive_pct': Decimal('0.00'),
            'neutral_pct': Decimal('0.00'),
            'negative_pct': Decimal('0.00'),
            'score': Decimal('0.00'),
            'benchmark': BENCHMARKS_1ST_KPI['well_being']
        }

    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, WELL_BEING_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['well_being']
    }

def calculate_respect_metrics(survey_responses):
    """Calculate respect-related metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, RESPECT_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['respect']
    }

def calculate_communication_metrics(survey_responses):
    """Calculate open communication metrics and return them in a dictionary."""
    if not OPEN_COMMUNICATION_QUESTIONS:
        return {
            'positive_pct': Decimal('0.00'),
            'neutral_pct': Decimal('0.00'),
            'negative_pct': Decimal('0.00'),
            'score': Decimal('0.00'),
            'benchmark': BENCHMARKS_1ST_KPI['open_communication']
        }

    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, OPEN_COMMUNICATION_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_1ST_KPI['open_communication']
    }

def calculate_recognition_metrics(survey_responses):
    """Calculate recognition metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, RECOGNITION_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_2ND_KPI['recognition']
    }

def calculate_motivation_metrics(survey_responses):
    """Calculate motivation metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, MOTIVATION_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score,
        'benchmark': BENCHMARKS_2ND_KPI['motivation']
    }

def calculate_proud_to_work_metrics(survey_responses):
    """Calculate proud to work here metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, PROUD_TO_WORK_HERE_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_saves_peoples_care_metrics(survey_responses):
    """Calculate metrics for saves people care."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, SAVES_PEOPLES_CARE_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_fair_promotion_metrics(survey_responses):
    """Calculate metrics for fair promotion."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, FAIR_PROMOTION_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_involvement_decision_metrics(survey_responses):
    """Calculate metrics for involvement in decision making."""
    if not INVOLVEMENT_DECISION_QUESTIONS:
        return {
            'positive_pct': Decimal('0.00'),
            'neutral_pct': Decimal('0.00'),
            'negative_pct': Decimal('0.00'),
            'score': Decimal('0.00')
        }

    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, INVOLVEMENT_DECISION_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_leadership_reachable_metrics(survey_responses):
    """Calculate metrics for leadership reachability."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, LEADERSHIP_REACHABLE_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_leadership_metrics(survey_responses):
    """Calculate leadership metrics and return them in a dictionary."""
    if not LEADERSHIP_QUESTIONS:
        return {
            'positive_pct': Decimal('0.00'),
            'neutral_pct': Decimal('0.00'),
            'negative_pct': Decimal('0.00'),
            'score': Decimal('0.00')
        }

    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, LEADERSHIP_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_policies_metrics(survey_responses):
    """Calculate policies metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, POLICIES_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_workplace_culture_metrics(survey_responses):
    """Calculate workplace culture metrics and return them in a dictionary."""
    if not WORKPLACE_CULTURE_QUESTIONS:
        return {
            'positive_pct': Decimal('0.00'),
            'neutral_pct': Decimal('0.00'),
            'negative_pct': Decimal('0.00'),
            'score': Decimal('0.00')
        }

    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, WORKPLACE_CULTURE_QUESTIONS
    )
    score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_all_category_metrics(survey_responses):
    """Calculate metrics for all question categories and return them in a dictionary."""
    return {
        'diversity': calculate_diversity_metrics(survey_responses),
        'equity': calculate_equity_metrics(survey_responses),
        'inclusion': calculate_inclusion_metrics(survey_responses),
        'credibility': calculate_credibility_metrics(survey_responses),
        'fairness': calculate_fairness_metrics(survey_responses),
        'workplace_satisfaction': calculate_workplace_satisfaction_metrics(survey_responses),
        'team_spirit': calculate_team_spirit_metrics(survey_responses),
        'well_being': calculate_wellbeing_metrics(survey_responses),
        'respect': calculate_respect_metrics(survey_responses),
        'open_communication': calculate_communication_metrics(survey_responses),
        'recognition': calculate_recognition_metrics(survey_responses),
        'motivation': calculate_motivation_metrics(survey_responses),
        'proud_to_work_here': calculate_proud_to_work_metrics(survey_responses),
        'saves_peoples_care': calculate_saves_peoples_care_metrics(survey_responses),
        'fair_promotion': calculate_fair_promotion_metrics(survey_responses),
        'involvement_decision': calculate_involvement_decision_metrics(survey_responses),
        'leadership_reachable': calculate_leadership_reachable_metrics(survey_responses),
        'leadership': calculate_leadership_metrics(survey_responses),
        'policies': calculate_policies_metrics(survey_responses),
        'workplace_culture': calculate_workplace_culture_metrics(survey_responses)
    }
