from flask import Flask, render_template, request, redirect, session ,url_for
import mysql.connector
from urllib.parse import urlparse

app = Flask(__name__)
app.secret_key = 'your_secret_key'

# Questions and options
QUESTIONS = [
 "   Does the organization hire and promote people fairly, giving everyone an equal chance?",  
    "Do you feel that your organization's policies support you, no matter your background of experience and knowledge?",
     "How often does your boss support fairness and equal treatment for everyone?",
        "Do you feel valued and appreciated for your work?",
    "Is there a strong sense of trust between employees and management in your organization?",
 "Do you feel that you are fairly paid for your skills, experience, and contributions?",
    ]

OPTIONS = [
    ["Always", "Sometimes", "Needs for Improvement", "Never"],
    ["Yes, Definitely", "Very few", "Needs for Improvement", "Never"],
    ["Regularly", "Occasionally", "Rarely", "Never"],

    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"],
   
    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"],
    ["Yes, absolutely", "Somewhat", "Not really", "Not at all"],
    
]

# MySQL connection
def get_db():
    return mysql.connector.connect(
        host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
        user="admin",
        password="master123",
        database="registration"
    )

@app.route('/', methods=['GET', 'POST'])
def root_redirect():
    return "❌ Access denied. Please use the URL with your email. (e.g., /<EMAIL>)", 403

@app.route('/<email_id>', methods=['GET', 'POST'])
def quiz(email_id):
    if '@' not in email_id or '.' not in email_id:
        return "❌ Invalid URL format. Expected email in the URL.", 400

    # Extract company from email
    company_name = email_id.split('@')[-1].split('.')[0]
    session['email_from_url'] = email_id
    session['company_name'] = company_name.capitalize()

    if request.method == 'POST':
        if 'step' not in session:

            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['step'] = 0
            session['answers'] = []
            return redirect(f"/{email_id}")
        else:
            if 'back' in request.form:
                if session['step'] > 0:
                    session['step'] -= 1
                    session['answers'].pop()
                return redirect(f"/{email_id}")
            else:
                selected_value = request.form.get('option')
                selected_option = selected_value[0]
                session['answers'].append((selected_option, selected_value))
                session['step'] += 1

                if session['step'] >= len(QUESTIONS):
                    db = get_db()
                    cursor = db.cursor()
                    form_url = request.url
                    for i, (opt, text) in enumerate(session['answers']):
                        cursor.execute(
                            "INSERT INTO student_data (gender, age_group, tenure_group, role, department, question_number, question_text, selected_text, form_url) "
                            "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)",
                            (session['gender'], session['age_group'], session['tenure_group'], session['role'], session['department'], i + 1, QUESTIONS[i], text, form_url)
                        )
                    db.commit()
                    cursor.close()
                    db.close()
                    session.clear()
                    return redirect(url_for('thank_you'))

                return redirect(f"/{email_id}")

    if 'step' not in session:
        return render_template('quiz.html', stage='user', company=session.get('company_name'))
    else:
        qnum = session['step']
        current_q = {
            "q": QUESTIONS[qnum],
            "options": OPTIONS[qnum]
        }
        return render_template('quiz.html', stage='question', question=current_q, qnum=qnum + 1, company=session.get('company_name'), QUESTIONS=QUESTIONS)

@app.route('/thankyou')
def thank_you():
    return render_template('thank-you.html')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8001)
