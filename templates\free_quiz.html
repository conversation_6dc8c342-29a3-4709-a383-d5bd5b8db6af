<!DOCTYPE html>
<html>
<head>
    <title>SURVEY PAGE</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <style>
        /* ===== Global Styles ===== */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            margin-top: 0px !important;
            position: static !important;
        }
        
       
        
        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        
        /* ===== Navigation ===== */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #000;
            padding: 10px 20px;
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 2px solid #444;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .nav-left .logo {
            width: auto;
            height: 50px;
         
            cursor: pointer;
            transition: transform 0.3s ease;
           
        }

        
#google_translate_element {
display: none !important;
margin-left: 30px;
margin-top: 0px; 

}
div[jsname="W297wb"] {
display: none !important;
}

.goog-te-gadget {
display: none !important;
}

.dropdown {
position: relative;
display: inline-block;
margin-top: 10px;
margin-right: 20px;
}


.goog-logo-link {
    display: none !important;
}

.goog-te-gadget {
    color: transparent !important;
}

.goog-te-gadget .goog-te-combo {
    color: black !important;
    margin: 0 !important;
}

.goog-te-gadget span {
    display: none !important;
}

.VIpgJd-ZVi9od-l4eHX-hSRGPd,
.VIpgJd-ZVi9od-ORHb-OEVmcd {
    display: none !important;
}
        .nav-left .logo:hover {
            transform: rotate(15deg) scale(1.1);
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        /* ===== Language Selector ===== */
        .language-dropdown {
            position: relative;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            padding: 8px 12px;
            cursor: pointer;
            border: 1px solid #686565;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: white;
            border-radius: 5px;
            font-size: 12px;
            width: 130px;
            color: black;
        }

        .dropdown-btn img {
            width: 20px;
            height: 15px;
            margin-right: 10px;
            border: 1px solid #eee;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            border: 1px solid #ccc;
            width: 100%;
            font-size:13px;
            z-index: 1000;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .dropdown-item {
            padding: 10px;
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #333;
            border-bottom: 1px solid #eee;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item img {
            width: 20px;
            height: 15px;
            margin-right: 10px;
            border: 1px solid #eee;
        }

        .dropdown-item:hover {
            background-color: #f1f1f1;
        }

        
        /* ===== Google Translate ===== */
        #google_translate_element {
            display: none !important;
        }
        h4{
            margin-left: 28%;
            font-size: 18px;
        }

        /* Hide Google Translate banner */
.goog-te-banner-frame.skiptranslate,
.goog-te-banner-frame,
.goog-logo-link,
#goog-gt-tt,
.goog-te-balloon-frame,
.goog-te-menu-value,
.goog-te-gadget-icon {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
}

.skiptranslate {
  display: none !important;
}

        /* ===== Form Styles ===== */
        .container {
            background-color: rgba(204, 203, 203, 0.76);
            padding: 60px;
            border-color: #000;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
            border: 1px solid #ddd;
            width: 1100px;
            
        }

        .container h1  {
            font-size: 40px;
            font-weight: bolder;
             text-transform: uppercase;
            
        }

        .container h2  {
            font-size: 30px;
             font-weight: bolder;
        }
        .container h3  {
            font-size: 25px;
            text-align: center;
        }
        
        .form-row {
            display: flex;
            margin-bottom: 20px;
            gap: 20px;
            justify-content: center
        }

        form p {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* Or your preferred font */
  font-size: 25px;
  font-weight: bold;}
        
        .form-group {
            max-width: 300px;
            flex: 1;
            margin-top: 25px;
        }

        .option-box {

             font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            font-size: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        select, input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        
        /* Consistent submit button style for both containers */
        .submit-btn {
            background-color: #979595;
            color: rgb(4, 0, 0);
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 20px;
            transition: background-color 0.3s ease;
        }
        
        .submit-btn:hover {
            background-color: #7a7878;
        }
        
        
        
        
        /* Container 1 submit button positioning */
        .container .submit-btn2 {
            margin-left: auto;
            display: block;
            margin-top: 15px;
        }
        .other-field {
            display: none;
            margin-top: -4px;
        }
        
        h5 {
            margin-top: 90px;
            color: #000000;
            font-size: 15px;
            text-align: start;
            
        }

        /* =============container 2============ */
       

        .container2 h2,
        .container2 h4 {
            text-align: center;
        }

       

        .container2 form p {
            font-weight: bold;
            font-size: 16px;
        }

        .container2 input[type="radio"] {
            margin-right: 10px;
        }

        .option-box {
            border: 1px solid #333;
            border-radius: 6px;
            width: 75%;
            padding: 10px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .option-box:hover {
            background-color: #0fb022;
        }

        .option-box input[type="radio"] {
            margin-right: 12px;
        }


        /* Container 1 submit button positioning */
        .container .submit-btn {
            margin-left: auto;
            display: block;
            margin-top: 20px;
        }
        
           .submit-btn2 {
            background-color:  #979595;
            color: rgb(4, 0, 0);
            padding: 10px 20px;
            border: none;
            margin-right: 25%;
            
            border-radius: 8px;
            cursor: pointer;
            font-size: 20px;
            transition: background-color 0.3s ease;
        }
        
        .submit-btn2:hover {
            background-color: #7a7878;
        }

        .button-row {
            margin-top: 30px;
        }

        .button-row button {
            padding: 10px 20px;
            /* margin-left: 1.7%; */
            height: 50px;
            font-size: 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            background-color: #979595;
            color: #000000;
            transition: background-color 0.3s ease;
        }

        .button-row button:hover {
            background-color: #7a7878;
        }
        .button-row2 {
           
            margin-top: -6%;
           
        }

        .button-row2 button {
            padding: 10px 20px;
            height: 30%;
            font-size: 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            background-color: #979595;
            color: #000000;
            transition: background-color 0.3s ease;
        }

        .button-row2 button:hover {
            background-color: #7a7878;
        }
        hr{
            border: 0;
            height: 2px;
            background-color: #000;
            margin: 10px 0;
        }

        /* ===== Progress Bar Styles ===== */
    .progress-container {
    width: 100%;
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    margin: 20px auto;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #127fa3;
    transition: width 0.3s ease;
}
/* #que{
    margin-left: 20px;
} */
.progress-text {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

#sec{
    margin-left: 30px;
}
    </style>
</head>
<body>
   <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-left">
            
                <img src="https://frandzzo-web-content.s3.amazonaws.com/images/logo-white.png" alt="Logo" class="logo">
            </a>
        </div>
        <div class="nav-right">
            <!-- Language Selector -->
            <div class="language-dropdown">
                <div id="google_translate_element"></div>
                <div class="dropdown">
                    <div class="dropdown-btn" id="dropdownBtn">
                        <img id="selected-flag" src="https://flagcdn.com/w320/us.png" alt="USA Flag" />
                        <span id="selected-language" class="notranslate">English</span>
                    </div>
                    <div class="dropdown-content" id="dropdownContent">
                        <div class="dropdown-item notranslate" data-value="en" data-text="English" data-flag="https://flagcdn.com/w320/us.png">
                            <img src="https://flagcdn.com/w320/us.png" alt="USA Flag" /> English
                        </div>
                          <div class="dropdown-item notranslate" data-value="en" data-text="English" data-flag="https://flagcdn.com/w320/us.png">
                            <img src="https://flagcdn.com/w320/in.png" alt="USA Flag" /> English
                        </div>
                           
                      
                         <div class="dropdown-item notranslate" data-value="ja" data-text="Japanese" data-flag="https://flagcdn.com/w320/jp.png">
                            <img src="https://flagcdn.com/w320/jp.png" alt="Japan Flag" /> Japanese
                        </div>
                        <div class="dropdown-item notranslate" data-value="de" data-text="German" data-flag="https://flagcdn.com/w320/de.png">
                            <img src="https://flagcdn.com/w320/de.png" alt="Germany Flag" /> German
                        </div>
                        <div class="dropdown-item notranslate" data-value="zh-TW" data-text="Chinese" data-flag="https://flagcdn.com/w320/cn.png">
                            <img src="https://flagcdn.com/w320/cn.png" alt="China Flag" /> Chinese
                        </div>
                        <div class="dropdown-item notranslate" data-value="sv" data-text="Swedish" data-flag="https://flagcdn.com/w320/se.png">
                            <img src="https://flagcdn.com/w320/se.png" alt="Sweden Flag" /> Swedish
                        </div>
                           <div class="dropdown-item notranslate" data-value="nl" data-text="Dutch" data-flag="https://flagcdn.com/w320/nl.png">
                            <img src="https://flagcdn.com/w320/nl.png" alt="Netherlands Flag" /> Dutch
                        </div>
                        <div class="dropdown-item notranslate" data-value="ar" data-text="Arabic" data-flag="https://flagcdn.com/w320/ae.png">
                            <img src="https://flagcdn.com/w320/ae.png" alt="UAE Flag" /> Arabic
                        </div>
                        <div class="dropdown-item notranslate" data-value="it" data-text="Italian" data-flag="https://flagcdn.com/w320/it.png">
                            <img src="https://flagcdn.com/w320/it.png" alt="Italy Flag" /> Italian
                        </div><div class="dropdown-item notranslate" data-value="ru" data-text="Russian" data-flag="https://flagcdn.com/w320/ru.png">
                            <img src="https://flagcdn.com/w320/ru.png" alt="Russia Flag" /> Russian
                        </div>
                        <div class="dropdown-item notranslate" data-value="ko" data-text="Korean" data-flag="https://flagcdn.com/w320/kr.png">
                            <img src="https://flagcdn.com/w320/kr.png" alt="South Korea Flag" /> Korean
                        </div>
                        <div class="dropdown-item notranslate" data-value="ms" data-text="Malay" data-flag="https://flagcdn.com/w320/my.png">
                            <img src="https://flagcdn.com/w320/my.png" alt="Malaysia Flag" /> Malay
                        </div>
                        <div class="dropdown-item notranslate" data-value="th" data-text="Thai" data-flag="https://flagcdn.com/w320/th.png">
                            <img src="https://flagcdn.com/w320/th.png" alt="Thailand Flag" /> Thai
                        </div>
                      
                      
                    </div>
                   
                </div>
    </div>
</nav>

    <div class="container">
        {% if company %}
            <h1>{{ company }}</h1>
            
        {% endif %}

        {% if stage == 'user' %}
        <h2>Employee Information</h2>
        <h3>Let's Start With Your Basic Details</h3>
        <hr>
        <div class="form-container">
            <form method="POST">
                <div class="form-row">
                    
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="gender">Gender</label>
                        <select id="gender" name="gender" required>
                            <option value="">Select</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="age_group">Age Group</label>
                        <select id="age_group" name="age_group" required>
                            <option value="">Select</option>
                            <option value="18-25">18-25</option>
                            <option value="26-35">26-35</option>
                            <option value="36-45">36-45</option>
                            <option value="46+">46+</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="tenure_group">Tenure Group</label>
                        <select id="tenure_group" name="tenure_group" required>
                            <option value="">Select</option>
                            <option value="0-1 year">0-1 year</option>
                            <option value="1-3 years">1-3 years</option>
                            <option value="3-5 years">3-5 years</option>
                            <option value="5+ years">5+ years</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role" name="role" required>
                            <option value="">Select</option>
                            
                            <option value="Junior Staff">Junior Staff</option>
                            <option value="Senior Staff">Senior Staff</option>
                            <option value="Manager">Manager</option>
                            <option value="Executive">Executive</option>
                            <option value="Trainee">Trainee</option>
                            <option value="Team">Team Member</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="department">Department</label>
                        
                         
                         <select id="department" name="department" required>
                        <option value="">Select</option>
                        <option value="HR">Human Resources & Admin</option>
                        <option value="Finance">Finance and Accounting</option>
                         <option value="Sales">Sales and Marketing</option>
                        <option value="Product">Product Development</option>
                        <option value="Technical">Technical</option>
                        <option value="Operations">Operations</option>
                        <option value="Procurements">Procurement</option>
                        <option value="Quality">Quality</option>
                        <option value="Business">Business Development</option>
                        <option value="Executive">Executive</option>
                        <option value="Leadership">Leadership</option>
                        <option value="Management">Management</option>
                        <option value="Others">Others (Please Specify)</option>
                    </select>


                    </div>
                    <div class="form-group">
                          <div id="otherDepartmentField" class="other-field">
                            <label for="other_department">Other</label>
                            <input type="text" id="other_department" name="other_department">
                        </div>
                    </div>
                </div>

                <input type="submit" class="submit-btn" value="Next">
                <!-- <h5>Powered By Frandzzo Global - Next Gen AI Tech</h5> -->
            </form>
        </div>
    </div>

    <div class="container2">
        {% elif stage == 'question' %}
            <h2>Amazing Place To Work</h2>
            <h3>Your Voice . Our Culture . A Better Tomorrow</h3>
            <hr>
            <div id="sec">
                <div class="progress-container">
                    <div class="progress-bar" style="width: {{ (qnum / QUESTIONS|length) * 100 }}%"></div>
                </div>
                <div class="progress-text">{{ qnum }} of {{ QUESTIONS|length }} questions</div>

                <form method="POST">
                    <p>{{ qnum }}. {{ question.q }}</p>
                    {% for opt in question.options %}
                        <label class="option-box">
                            <input type="radio" name="option" value="{{ opt }}" required>
                            {{ opt }}
                        </label>
                    {% endfor %}
                    <br>

                    <div class="button-row">
                        {% if qnum > 1 %}
                            <button type="submit" name="back" value="true">Back</button>
                        {% endif %}
                    </div>

                    <div class="button-row2">
                        <input type="submit" class="submit-btn2" value="Next">
                    </div>

                    <h5>Powered By Frandzzo Global - Next Gen AI Tech</h5>
                </form>
            </div>
        {% endif %}
    </div>

    

    <script>
        // Show other department field when "Others" is selected
        document.getElementById('department').addEventListener('change', function() {
            const otherField = document.getElementById('otherDepartmentField');
            if (this.value === 'Others') {
                otherField.style.display = 'block';
            } else {
                otherField.style.display = 'none';
            }
        });

        
    </script>

    <script>
        function googleTranslateElementInit() {
        new google.translate.TranslateElement(
          { pageLanguage: "en" },
          "google_translate_element"
        );
        }
        const dropdownBtn = document.getElementById("dropdownBtn");
        const dropdownContent = document.getElementById("dropdownContent");
        
        dropdownBtn.addEventListener("click", function () {
        dropdownContent.style.display =
          dropdownContent.style.display === "block" ? "none" : "block";
        });
        
        // Close dropdown when clicking outside
        document.addEventListener("click", function(event) {
        if (!dropdownBtn.contains(event.target) && !dropdownContent.contains(event.target)) {
          dropdownContent.style.display = "none";
        }
        });
        
        document.querySelectorAll(".dropdown-item").forEach((item) => {
        item.addEventListener("click", function () {
          const flagSrc = this.getAttribute("data-flag");
          const languageText = this.getAttribute("data-text");
          const lang = this.getAttribute("data-value");
        
          document.getElementById("selected-flag").src = flagSrc;
          document.getElementById("selected-language").textContent = languageText;
        
          dropdownContent.style.display = "none";
        
          const languageSelect = document.querySelector("select.goog-te-combo");
          if (languageSelect) {
            languageSelect.value = lang;
            languageSelect.dispatchEvent(new Event("change"));
          }
        });
        });
    </script>
    <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
     <script type="text/javascript" src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

</body>
</html>