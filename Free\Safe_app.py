from flask import Flask, render_template, request, redirect, session ,url_for ,jsonify, render_template_string
import mysql.connector
import razorpay
import json
import hmac
from model_loader import predict_sentiment
from preprocess import refine_sentence
import pandas as pd
import hashlib
from datetime import datetime
from flask_cors import CORS
import os
from tablecreation import create_company_scores_table
from datetime import datetime, timedelta
from action_tablecreation import create_action_table_scores
from survey_metrics_calculator import (
    calculate_total_responses, calculate_sentiment_percentages, calculate_response_rate, calculate_dei_score,
    calculate_department_metrics, calculate_experience_metrics, calculate_role_engagement_metrics, calculate_all_voice_metrics,
    calculate_diversity_metrics, calculate_equity_metrics, calculate_inclusion_metrics, calculate_engagement,
    calculate_credibility_metrics, calculate_fairness_metrics, calculate_workplace_satisfaction_metrics, 
    calculate_team_spirit_metrics, calculate_wellbeing_metrics, calculate_respect_metrics, calculate_communication_metrics, 
    calculate_recognition_metrics, calculate_motivation_metrics, calculate_proud_to_work_metrics, 
    calculate_people_care_metrics, calculate_fair_promotion_metrics, calculate_involvement_decision_metrics,    calculate_leadership_reachable_metrics, calculate_leadership_metrics, calculate_policies_metrics, 
    calculate_workplace_culture_metrics, calculate_gender_metrics, calculate_strategic_alignment_gender_metrics)

from action_table_calculations import (calculate_sentiment_percentages, calculate_category_sentiment_percentages, 
                                       calculate_action_score, calculate_communication_metrics, calculate_leadership_effectiveness_metrics, 
                                       calculate_work_life_balance_metrics, calculate_career_development_metrics, calculate_recognition_rewards_metrics, 
                                       calculate_employee_engagement_metrics, calculate_workplace_environment_metrics, calculate_inclusion_diversity_metrics, 
                                       calculate_compensation_transparency_metrics, calculate_feedback_mechanisms_metrics, calculate_organizational_transparency_metrics, 
                                       calculate_manager_employee_relationship_metrics, calculate_psychological_safety_metrics, calculate_mission_values_alignment_metrics,
                                       calculate_innovation_creativity_metrics)



app = Flask(__name__, template_folder='templates')
app.secret_key = 'your_secret_key'
CORS(app)

# Questions and options - Full version (20 questions)
QUESTIONS = [
    "Does the organization hire and promote people fairly, giving everyone an equal chance?",
    "Do you feel that your organization's policies support you, no matter your background of experience and knowledge?",
    "Do you feel that your suggestions are valued and that everyone is included at work?",
    "Do you have career development opportunities to learn and grow in your current role?",
    "How often does your boss support fairness and equal treatment for everyone?",
    "Do you feel your boss supported in learning new skills and growing in your job?",
    "Does your organization support a good balance between Work and personal life? ",
    "Do you feel your manager listens to your concerns and takes action when needed?",
    "Do you feel supported and respected at work?",
    "Do you feel valued and appreciated for your work?",
    "Do you believe your company's leadership effectively communicates the organization's vision and values?",
    "Do you feel safe at work place harassment, threats, or discrimination?",
    "Do you feel your boss is committed to fostering equality at organization?",
    "Does your organization foster an environment where employees can voice their opinions without fear of retaliation?",
    "Is there a strong sense of trust between employees and management in your organization?",
    "Do you feel that you are fairly paid for your skills, experience, and contributions?",
    "Do you feel that your workload is manageable and allows you to focus effectively on your tasks?",
    "Do you feel your organization provides enough training to help employees understand fairness and respect at work?",
    "Do you feel included in organization discussions and decisions that impact your role?",
    "Do you feel the customer feels happy about your organization culture?"
]

# Free trial questions - Exactly in order [1,2,5,16,10,15] as requested
FREE_QUESTIONS = [
    "Does the organization hire and promote people fairly, giving everyone an equal chance?",                    # Question 1
    "Do you feel that your organization's policies support you, no matter your background of experience and knowledge?",  # Question 2
    "How often does your boss support fairness and equal treatment for everyone?",                              # Question 5
    "Do you feel that you are fairly paid for your skills, experience, and contributions?",                    # Question 16
    "Do you feel valued and appreciated for your work?",                                                        # Question 10
    "Is there a strong sense of trust between employees and management in your organization?"                   # Question 15
]

# Mapping from free question index to original question number
FREE_TO_ORIGINAL_MAPPING = [1, 2, 5, 16, 10, 15]

OPTIONS = [
    ["Always", "Sometimes", "Needs for Improvement", "Never"],
    ["Yes, Definitely", "Very few", "Needs for Improvement", "Never"],
    ["Always", "Frequently", "Sometimes", "Never"],
    ["Yes, always", "Sometimes", "Rarely", "Never"],
    ["Regularly", "Occasionally", "Rarely", "Never"],
    ["Yes, always", "Most of the time", "Sometimes", "No not at all"],
    ["Very Well", "Somewhat Well", "Needs Improvement", "Not at all"],
    ["Yes, always", "Sometimes", "Rarely", "Never"],
    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"],
    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"],
    ["Always", "Sometimes", "Needs for Improvement", "Never"],
    ["Very Comfortable", "Slightly Unsafe", "Unsafe", "Not Safe at all"],
    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"],
    ["Always", "Sometimes", "Needs for Improvement", "Never"],
    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"],
    ["Yes, absolutely", "Somewhat", "Not really", "Not at all"],
    ["Yes, Always", "Sometimes", "Rarely", "No, not at all"],
    ["Yes, Very well", "Reasonably Well", "Needs for Improvement", "No, not at all"],
    ["Yes, always", "Sometimes", "Rarely", "Never"],
    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"]
]

# Free trial options - Arranged according to the exact order [1,2,5,16,10,15]
FREE_OPTIONS = [
    ["Always", "Sometimes", "Needs for Improvement", "Never"],                    # Question 1: Organization hire and promote fairly
    ["Yes, Definitely", "Very few", "Needs for Improvement", "Never"],           # Question 2: Organization policies support you
    ["Regularly", "Occasionally", "Rarely", "Never"],                            # Question 5: Boss support fairness and equal treatment
    ["Yes, absolutely", "Somewhat", "Not really", "Not at all"],                 # Question 16: Fairly paid for skills and contributions
    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"],          # Question 10: Feel valued and appreciated
    ["Yes, always", "Most of the time", "Sometimes", "No, not at all"]           # Question 15: Trust between employees and management
]

# MySQL connection
db = mysql.connector.connect(
    host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
    user="admin",
    password="master123",
    database="registration"
)
def get_db():
    return mysql.connector.connect(
        host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
        user="admin",
        password="master123",
        database="registration"
    )
DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

# Company scores table creation
create_company_scores_table(DB_CONFIG)
create_action_table_scores(DB_CONFIG)

# Razorpay API Keys
RAZORPAY_KEY_ID = "***********************"
RAZORPAY_KEY_SECRET = "bTunaUu989efRjTBCegwxSam"

# Razorpay client initialization
razorpay_client = razorpay.Client(auth=(RAZORPAY_KEY_ID, RAZORPAY_KEY_SECRET))



def get_db_connection():
    """Create and return a connection to the database"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except mysql.connector.Error as err:
        print(f"Error connecting to MySQL: {err}")
        return None

def render_free_template(template_name, **context):
    """Render template from the free folder"""
    try:
        with open(f'free/{template_name}', 'r', encoding='utf-8') as f:
            template_content = f.read()

        # Simple template variable replacement
        for key, value in context.items():
            if isinstance(value, dict):
                # Handle nested dictionaries (like question objects)
                if key == 'question':
                    template_content = template_content.replace('{{ question.q }}', str(value.get('q', '')))
                    # Handle options
                    if 'options' in value:
                        options_html = ''
                        for i, option in enumerate(value['options']):
                            checked = 'checked' if context.get('selected_answer') == option else ''
                            options_html += f'''
                            <div class="option-box">
                                <input type="radio" name="option" value="{option}" id="option{i}" {checked}>
                                <label for="option{i}">{option}</label>
                            </div>
                            '''
                        template_content = template_content.replace('<!-- OPTIONS_PLACEHOLDER -->', options_html)
            else:
                template_content = template_content.replace('{{ ' + key + ' }}', str(value))

        return template_content
    except FileNotFoundError:
        return f"Template {template_name} not found in free folder"

def get_end_date_by_email(email_id):
    conn = get_db()
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT surveyEndDate FROM invoicek WHERE contactEmail = %s", (email_id,))
        result = cursor.fetchone()

        # Make sure we've consumed all results before closing
        while cursor.fetchone() is not None:
            pass

        return result[0] if result else None
    finally:
        # Always close the cursor before closing the connection
        cursor.close()
        conn.close()
def payment(survey_url):
    db = get_db()
    cur = db.cursor()
    try:
        cur.execute(("""
        select company_name, industry_type,country,product_name,num_surveys,contactEmail,leaderName,leaderRole,
                    surveyStartDate,surveyEndDate,payment_status,survey_url from invoicek where survey_url = %s"""), (survey_url,) )
        row = cur.fetchone()

        # Make sure we've consumed all results before closing
        while cur.fetchone() is not None:
            pass

        return row
    finally:
        # Always close the cursor to avoid resource leaks
        cur.close()
        # Don't close the db connection here as it might be needed elsewhere

@app.route('/', methods=['GET', 'POST'])
def root_redirect():
    return "❌ Access denied. Please use the URL with your email. (e.g., /<EMAIL>)", 403

@app.route('/survey/<email_id>', methods=['GET', 'POST'])

def quiz(email_id):
    if '@' not in email_id or '.' not in email_id:
        return """
            <script>
                alert("❌ Invalid URL format. Expected email in the URL.");
                window.location.href = "/";
            </script>
        """, 400

    # 🔒 Check for expiry date
    end_date_str = get_end_date_by_email(email_id)
    if not end_date_str:
        return """
            <script>
                alert("❌ Email ID not found in the expiry database.");
                window.location.href = "/";
            </script>
        """, 404

    try:
        end_date = datetime.strptime(str(end_date_str), '%m/%d/%Y').date()
        today = datetime.today().date()

        if end_date < today:
            return """
                <script>
                    alert("❌ This survey link has expired.");
                    window.location.href = "/";
                </script>
            """, 403
    except ValueError:
        return """
            <script>
                alert("❌ Invalid date format in the database.");
                window.location.href = "/";
            </script>
        """, 500

    # ✅ Your existing logic for full_url verification and other checks follows here...

    # Construct the full URL
    survey_url =  'https://gp360e-pulse-survey.frandzzo.com/survey/<EMAIL>' #request.url  # e.g., http://127.0.0.1:8000/<EMAIL>

    # Check if full URL is allowed
    db = get_db()
    cursor = db.cursor(dictionary=True)


    cursor.execute("SELECT 1 FROM invoicek WHERE survey_url = %s", (survey_url,))
    allowed = cursor.fetchone()

        # Make sure we've consumed all results
    while cursor.fetchone() is not None:
        pass

    if not allowed:
        return """
            <script>
                alert("❌ Access denied. This link is not authorized.");
                window.location.href = "/";
            </script>
        """, 403

    # Validate survey limit - use a new cursor for this query
    cursor2 = db.cursor()
    try:
        cursor2.execute("SELECT num_surveys FROM invoicek WHERE contactEmail = %s", (email_id,))
        result1 = cursor2.fetchone()

    finally:
        cursor2.close()

    # Create a new cursor for the third query
    cursor3 = db.cursor()
    try:
        cursor3.execute("SELECT distinct(unique_id) FROM student_data where form_url = %s", (survey_url,))
        result2 = cursor3.fetchall()


    finally:
        cursor3.close()

    total_respo = len(result2) if result2 else 0

    if not result1 :
        return """
            <script>
                alert("❌ Email ID not found in one or both tables.");
                window.location.href = "/";
            </script>
        """, 403


    num_survey = result1[0]

    if total_respo >= num_survey:
        return """
            <script>
                alert("❌ Survey limit reached. You cannot submit another response.");
                window.location.href = "/";
            </script>
        """, 403

    # Extract company from email
    company_name = email_id.split('@')[-1].split('.')[0]
    session['email_from_url'] = email_id
    session['company_name'] = company_name.capitalize()

    if request.method == 'POST':
        if 'step' not in session:
            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['step'] = 0
            session['answers'] = []
            return redirect(f"/survey/{email_id}")
        else:
            if 'back' in request.form:
                if session['step'] > 0:
                    session['step'] -= 1
                return redirect(f"/survey/{email_id}")
            else:
                selected_value = request.form.get('option')
                if not selected_value:
                    qnum = session['step']
                    current_q = {
                        "q": QUESTIONS[qnum],
                        "options": OPTIONS[qnum]
                    }
                    return render_template('quiz.html', stage='question', question=current_q,
                                         qnum=qnum + 1, company=session.get('company_name'),
                                         QUESTIONS=QUESTIONS, error="Please select an option")

                selected_option = selected_value[0]
                # Update or append the answer
                if session['step'] < len(session['answers']):
                    session['answers'][session['step']] = (selected_option, selected_value)
                else:
                    session['answers'].append((selected_option, selected_value))
                session['step'] += 1

                if session['step'] >= len(QUESTIONS):
                    db = get_db()
                    cursor = db.cursor()
                    payment_data = payment(survey_url) 
                    # Generate random unique_id for better clarity to identify single person responses
                    import random
                    session['unique_id'] = str(random.randint(100000, 999999))  # 6-digit random unique ID

                    # Ensure we only process answers for questions that exist
                    for i, (_, text) in enumerate(session['answers']):
                        if i < len(QUESTIONS):
                            sentiment = predict_sentiment(refine_sentence(QUESTIONS[i], text))
                            cursor.execute(
                                "INSERT INTO student_data (unique_id, gender, age_group, tenure_group, role, department, question_number, question_text, selected_text, form_url,predicted_sentiment) "
                                "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s)",
                                (session['unique_id'], session['gender'],
                                 session['age_group'], session['tenure_group'], session['role'],
                                 session['department'], i + 1, QUESTIONS[i], text, survey_url, sentiment)
                            )

                    # Commit only once after all inserts
                    db.commit()

                    # Create a new cursor for the select query to avoid "Unread result found" error
                    select_cursor = db.cursor()
                    select_cursor.execute(('select unique_id,predicted_sentiment,department,tenure_group,role,gender,question_number from student_data where form_url = %s'),(survey_url,))
                    survey_responses = pd.DataFrame(select_cursor.fetchall(), columns=['roll_no', 'predicted_sentiment', 'department', 'tenure', 'role', 'gender','question_number'])
                    select_cursor.close()

                    if survey_responses.empty:
                        raise Exception("No survey responses found")

                    # Calculate basic metrics
                    total_responses = calculate_total_responses(survey_responses)
                    response_rate = calculate_response_rate(total_responses, payment_data[4])
                    positive_pct, neutral_pct, negative_pct = calculate_sentiment_percentages(survey_responses)
                    dei_score = calculate_dei_score(positive_pct, neutral_pct, negative_pct)

                    # Calculate all category metrics
                    diversity_metrics = calculate_diversity_metrics(survey_responses)
                    equity_metrics = calculate_equity_metrics(survey_responses)
                    inclusion_metrics = calculate_inclusion_metrics(survey_responses)
                    engagement_metrics = calculate_engagement(survey_responses)

                    # Calculate additional metrics
                    credibility_metrics = calculate_credibility_metrics(survey_responses)
                    fairness_metrics = calculate_fairness_metrics(survey_responses)
                    workplace_satisfaction_metrics = calculate_workplace_satisfaction_metrics(survey_responses)
                    team_spirit_metrics = calculate_team_spirit_metrics(survey_responses)
                    wellbeing_metrics = calculate_wellbeing_metrics(survey_responses)
                    respect_metrics = calculate_respect_metrics(survey_responses)
                    communication_metrics = calculate_communication_metrics(survey_responses)
                    recognition_metrics = calculate_recognition_metrics(survey_responses)
                    motivation_metrics = calculate_motivation_metrics(survey_responses)

                    # Calculate specific metrics
                    proud_to_work_metrics = calculate_proud_to_work_metrics(survey_responses)
                    people_care_metrics = calculate_people_care_metrics(survey_responses)
                    fair_promotion_metrics = calculate_fair_promotion_metrics(survey_responses)
                    involvement_decision_metrics = calculate_involvement_decision_metrics(survey_responses)
                    leadership_reachable_metrics = calculate_leadership_reachable_metrics(survey_responses)

                    # Calculate LPW metrics
                    leadership_metrics = calculate_leadership_metrics(survey_responses)
                    policies_metrics = calculate_policies_metrics(survey_responses)
                    workplace_culture_metrics = calculate_workplace_culture_metrics(survey_responses)

                    # Calculate demographic metrics
                    department_metrics = calculate_department_metrics(survey_responses)
                    experience_metrics = calculate_experience_metrics(survey_responses)
                    role_metrics = calculate_role_engagement_metrics(survey_responses)
                    gender_metrics = calculate_gender_metrics(survey_responses)
                    emp_voice_metrics = calculate_all_voice_metrics(survey_responses)

                    # First insert/update basic metrics in companyscore
                    cursor.execute('''
                        INSERT INTO companyscore (
                            company_name, industry_type, country, product_name, leader_name, leader_role, contact_email,
                            survey_start_date, survey_end_date, payment_status, survey_url, num_surveys, total_responses,
                            response_rate, dei_positive_pct, dei_neutral_pct, dei_negative_pct, dei_score
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                            total_responses = VALUES(total_responses),
                            response_rate = VALUES(response_rate),
                            dei_positive_pct = VALUES(dei_positive_pct),
                            dei_neutral_pct = VALUES(dei_neutral_pct),
                            dei_negative_pct = VALUES(dei_negative_pct),
                            dei_score = VALUES(dei_score)
                    ''', (
                        payment_data[0], payment_data[1], payment_data[2], payment_data[3], payment_data[6],
                        payment_data[7], payment_data[5], payment_data[8], payment_data[9], payment_data[10],
                        payment_data[11], payment_data[4], total_responses, response_rate, positive_pct,
                        neutral_pct, negative_pct, dei_score
                    ))


                    # Update DEI metrics
                    cursor.execute('''
                        UPDATE companyscore SET
                        diversity_score = %s, diversity_positive_pct = %s, diversity_neutral_pct = %s, diversity_negative_pct = %s,
                        equity_score = %s, equity_positive_pct = %s, equity_neutral_pct = %s, equity_negative_pct = %s,
                        inclusion_score = %s, inclusion_positive_pct = %s, inclusion_neutral_pct = %s, inclusion_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (
                        diversity_metrics['score'], diversity_metrics['positive_pct'], diversity_metrics['neutral_pct'],
                        diversity_metrics['negative_pct'], equity_metrics['score'], equity_metrics['positive_pct'],
                        equity_metrics['neutral_pct'], equity_metrics['negative_pct'], inclusion_metrics['score'],
                        inclusion_metrics['positive_pct'], inclusion_metrics['neutral_pct'], inclusion_metrics['negative_pct'],
                        payment_data[11]
                    ))

                    # Update engagement metrics
                    cursor.execute('''
                        UPDATE companyscore SET
                        culture_engagement_score = %s, culture_engagement_positive_pct = %s,
                        culture_engagement_neutral_pct = %s, culture_engagement_negative_pct = %s,
                        strategic_alignment_score = %s, strategic_alignment_positive_pct = %s,
                        strategic_alignment_neutral_pct = %s, strategic_alignment_negative_pct = %s,
                        support_motivation_score = %s, support_motivation_positive_pct = %s,
                        support_motivation_neutral_pct = %s, support_motivation_negative_pct = %s,
                        skill_development_score = %s, skill_development_positive_pct = %s,
                        skill_development_neutral_pct = %s, skill_development_negative_pct = %s,
                        engagement_rate_score = %s, engagement_rate_positive_pct = %s,
                        engagement_rate_neutral_pct = %s, engagement_rate_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (
                        engagement_metrics['culture_of_engagement']['score'], engagement_metrics['culture_of_engagement']['positive_pct'],
                        engagement_metrics['culture_of_engagement']['neutral_pct'], engagement_metrics['culture_of_engagement']['negative_pct'],
                        engagement_metrics['strategic_alignment']['score'], engagement_metrics['strategic_alignment']['positive_pct'],
                        engagement_metrics['strategic_alignment']['neutral_pct'], engagement_metrics['strategic_alignment']['negative_pct'],
                        engagement_metrics['support_and_motivation']['score'], engagement_metrics['support_and_motivation']['positive_pct'],
                        engagement_metrics['support_and_motivation']['neutral_pct'], engagement_metrics['support_and_motivation']['negative_pct'],
                        engagement_metrics['skill_development']['score'], engagement_metrics['skill_development']['positive_pct'],
                        engagement_metrics['skill_development']['neutral_pct'], engagement_metrics['skill_development']['negative_pct'],
                        engagement_metrics['engagement_rate']['score'], engagement_metrics['engagement_rate']['positive_pct'],
                        engagement_metrics['engagement_rate']['neutral_pct'], engagement_metrics['engagement_rate']['negative_pct'],
                        payment_data[11]
                    ))

                    # Update other category metrics
                    cursor.execute('''
                        UPDATE companyscore SET
                        credibility_score = %s, credibility_positive_pct = %s, credibility_neutral_pct = %s, credibility_negative_pct = %s,
                        fairness_score = %s, fairness_positive_pct = %s, fairness_neutral_pct = %s, fairness_negative_pct = %s,
                        workplace_satisfaction_score = %s, workplace_satisfaction_positive_pct = %s,
                        workplace_satisfaction_neutral_pct = %s, workplace_satisfaction_negative_pct = %s,
                        team_spirit_score = %s, team_spirit_positive_pct = %s, team_spirit_neutral_pct = %s, team_spirit_negative_pct = %s,
                        well_being_score = %s, well_being_positive_pct = %s, well_being_neutral_pct = %s, well_being_negative_pct = %s,
                        respect_score = %s, respect_positive_pct = %s, respect_neutral_pct = %s, respect_negative_pct = %s,
                        open_communication_score = %s, open_communication_positive_pct = %s,
                        open_communication_neutral_pct = %s, open_communication_negative_pct = %s,
                        recognition_score = %s, recognition_positive_pct = %s, recognition_neutral_pct = %s, recognition_negative_pct = %s,
                        motivation_score = %s, motivation_positive_pct = %s, motivation_neutral_pct = %s, motivation_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (
                        credibility_metrics['score'], credibility_metrics['positive_pct'], credibility_metrics['neutral_pct'],
                        credibility_metrics['negative_pct'], fairness_metrics['score'], fairness_metrics['positive_pct'],
                        fairness_metrics['neutral_pct'], fairness_metrics['negative_pct'], workplace_satisfaction_metrics['score'],
                        workplace_satisfaction_metrics['positive_pct'], workplace_satisfaction_metrics['neutral_pct'],
                        workplace_satisfaction_metrics['negative_pct'], team_spirit_metrics['score'], team_spirit_metrics['positive_pct'],
                        team_spirit_metrics['neutral_pct'], team_spirit_metrics['negative_pct'], wellbeing_metrics['score'],
                        wellbeing_metrics['positive_pct'], wellbeing_metrics['neutral_pct'], wellbeing_metrics['negative_pct'],
                        respect_metrics['score'], respect_metrics['positive_pct'], respect_metrics['neutral_pct'],
                        respect_metrics['negative_pct'], communication_metrics['score'], communication_metrics['positive_pct'],
                        communication_metrics['neutral_pct'], communication_metrics['negative_pct'], recognition_metrics['score'],
                        recognition_metrics['positive_pct'], recognition_metrics['neutral_pct'], recognition_metrics['negative_pct'],
                        motivation_metrics['score'], motivation_metrics['positive_pct'], motivation_metrics['neutral_pct'],
                        motivation_metrics['negative_pct'], payment_data[11]
                    ))

                    # Update LPW metrics
                    cursor.execute('''
                        UPDATE companyscore SET
                        leadership_score = %s, leadership_positive_pct = %s, leadership_neutral_pct = %s, leadership_negative_pct = %s,
                        policies_score = %s, policies_positive_pct = %s, policies_neutral_pct = %s, policies_negative_pct = %s,
                        workplace_culture_score = %s, workplace_culture_positive_pct = %s,
                        workplace_culture_neutral_pct = %s, workplace_culture_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (
                        leadership_metrics['score'], leadership_metrics['positive_pct'], leadership_metrics['neutral_pct'],
                        leadership_metrics['negative_pct'], policies_metrics['score'], policies_metrics['positive_pct'],
                        policies_metrics['neutral_pct'], policies_metrics['negative_pct'], workplace_culture_metrics['score'],
                        workplace_culture_metrics['positive_pct'], workplace_culture_metrics['neutral_pct'],
                        workplace_culture_metrics['negative_pct'], payment_data[11]
                    ))

                    # Update new metrics
                    cursor.execute('''
                        UPDATE companyscore SET
                        proud_to_work_score = %s, proud_to_work_positive_pct = %s, proud_to_work_neutral_pct = %s, proud_to_work_negative_pct = %s,
                        people_care_score = %s, people_care_positive_pct = %s, people_care_neutral_pct = %s, people_care_negative_pct = %s,
                        fair_promotion_score = %s, fair_promotion_positive_pct = %s, fair_promotion_neutral_pct = %s, fair_promotion_negative_pct = %s,
                        involvement_decision_score = %s, involvement_decision_positive_pct = %s, involvement_decision_neutral_pct = %s, involvement_decision_negative_pct = %s,
                        leadership_reachable_score = %s, leadership_reachable_positive_pct = %s, leadership_reachable_neutral_pct = %s, leadership_reachable_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (
                        proud_to_work_metrics['score'], proud_to_work_metrics['positive_pct'], proud_to_work_metrics['neutral_pct'],
                        proud_to_work_metrics['negative_pct'], people_care_metrics['score'], people_care_metrics['positive_pct'],
                        people_care_metrics['neutral_pct'], people_care_metrics['negative_pct'], fair_promotion_metrics['score'],
                        fair_promotion_metrics['positive_pct'], fair_promotion_metrics['neutral_pct'], fair_promotion_metrics['negative_pct'],
                        involvement_decision_metrics['score'], involvement_decision_metrics['positive_pct'], involvement_decision_metrics['neutral_pct'],
                        involvement_decision_metrics['negative_pct'], leadership_reachable_metrics['score'], leadership_reachable_metrics['positive_pct'],
                        leadership_reachable_metrics['neutral_pct'], leadership_reachable_metrics['negative_pct'], payment_data[11]
                    ))

                    # Update department metrics
                    department_values = []
                    for key, value in department_metrics.items():
                        department_values.append(value)
                    cursor.execute('''
                        UPDATE companyscore SET
                        dept_HR_and_Admin_dei_score = %s, dept_HR_and_Admin_positive_pct = %s,
                        dept_HR_and_Admin_neutral_pct = %s, dept_HR_and_Admin_negative_pct = %s,
                        dept_Finance_and_accounting_dei_score = %s, dept_Finance_and_accounting_positive_pct = %s,
                        dept_Finance_and_accounting_neutral_pct = %s, dept_Finance_and_accounting_negative_pct = %s,
                        dept_Sales_marketing_dei_score = %s, dept_Sales_marketing_positive_pct = %s,
                        dept_Sales_marketing_neutral_pct = %s, dept_Sales_marketing_negative_pct = %s,
                        dept_Product_development_dei_score = %s, dept_Product_development_positive_pct = %s,
                        dept_Product_development_neutral_pct = %s, dept_Product_development_negative_pct = %s,
                        dept_Technical_dei_score = %s, dept_Technical_positive_pct = %s,
                        dept_Technical_neutral_pct = %s, dept_Technical_negative_pct = %s,
                        dept_Operations_dei_score = %s, dept_Operations_positive_pct = %s,
                        dept_Operations_neutral_pct = %s, dept_Operations_negative_pct = %s,
                        dept_Procurement_dei_score = %s, dept_Procurement_positive_pct = %s,
                        dept_Procurement_neutral_pct = %s, dept_Procurement_negative_pct = %s,
                        dept_Quality_dei_score = %s, dept_Quality_positive_pct = %s,
                        dept_Quality_neutral_pct = %s, dept_Quality_negative_pct = %s,
                        dept_Business_Development_dei_score = %s, dept_Business_Development_positive_pct = %s,
                        dept_Business_Development_neutral_pct = %s, dept_Business_Development_negative_pct = %s,
                        dept_executive_dei_score = %s, dept_executive_positive_pct = %s,
                        dept_executive_neutral_pct = %s, dept_executive_negative_pct = %s,
                        dept_leadership_dei_score = %s, dept_leadership_positive_pct = %s,
                        dept_leadership_neutral_pct = %s, dept_leadership_negative_pct = %s,
                        dept_Management_dei_score = %s, dept_Management_positive_pct = %s,
                        dept_Management_neutral_pct = %s, dept_Management_negative_pct = %s,
                        dept_others_dei_score = %s, dept_others_positive_pct = %s,
                        dept_others_neutral_pct = %s, dept_others_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (*department_values, payment_data[11]))

                    # Update experience metrics
                    experience_values = []
                    for key, value in experience_metrics.items():
                        experience_values.append(value)
                    cursor.execute('''
                        UPDATE companyscore SET
                        exp_1to2_dei_score = %s, exp_1to2_positive_pct = %s, exp_1to2_neutral_pct = %s, exp_1to2_negative_pct = %s,
                        exp_3to5_dei_score = %s, exp_3to5_positive_pct = %s, exp_3to5_neutral_pct = %s, exp_3to5_negative_pct = %s,
                        exp_above5_dei_score = %s, exp_above5_positive_pct = %s, exp_above5_neutral_pct = %s, exp_above5_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (*experience_values, payment_data[11]))

                    # Update role metrics
                    role_values = []
                    for key, value in role_metrics.items():
                        role_values.append(value)
                    cursor.execute('''
                        UPDATE companyscore SET
                        role_Junior_staff_engagement_score = %s, role_Junior_staff_positive_pct = %s,
                        role_Junior_staff_neutral_pct = %s, role_Junior_staff_negative_pct = %s,
                        role_Senior_staff_engagement_score = %s, role_Senior_staff_positive_pct = %s,
                        role_Senior_staff_neutral_pct = %s, role_Senior_staff_negative_pct = %s,
                        role_manager_engagement_score = %s, role_manager_positive_pct = %s,
                        role_manager_neutral_pct = %s, role_manager_negative_pct = %s,
                        role_executive_engagement_score = %s, role_executive_positive_pct = %s,
                        role_executive_neutral_pct = %s, role_executive_negative_pct = %s,
                        role_trainee_engagement_score = %s, role_trainee_positive_pct = %s,
                        role_trainee_neutral_pct = %s, role_trainee_negative_pct = %s,
                        role_team_member_engagement_score = %s, role_team_member_positive_pct = %s,
                        role_team_member_neutral_pct = %s, role_team_member_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (*role_values, payment_data[11]))

                    # Update gender metrics
                    gender_values = []
                    for key, value in gender_metrics.items():
                        gender_values.append(value)
                    cursor.execute('''
                        UPDATE companyscore SET
                        gender_male_dei_score = %s, gender_male_positive_pct = %s,
                        gender_male_neutral_pct = %s, gender_male_negative_pct = %s,
                        gender_female_dei_score = %s, gender_female_positive_pct = %s,
                        gender_female_neutral_pct = %s, gender_female_negative_pct = %s,
                        gender_others_dei_score = %s, gender_others_positive_pct = %s,
                        gender_others_neutral_pct = %s, gender_others_negative_pct = %s
                        WHERE survey_url = %s
                    ''', (*gender_values, payment_data[11]))

                    # Update employee voice metrics
                    voice_values = []
                    for key, metrics_tuple in emp_voice_metrics.items():
                        voice_values.extend(metrics_tuple)
                    cursor.execute('''
                        UPDATE companyscore SET
                        emp_voice1_positive_pct = %s, emp_voice1_neutral_pct = %s, emp_voice1_negative_pct = %s, emp_voice1_score = %s, emp_voice1_priority = %s,
                        emp_voice2_positive_pct = %s, emp_voice2_neutral_pct = %s, emp_voice2_negative_pct = %s, emp_voice2_score = %s, emp_voice2_priority = %s,
                        emp_voice3_positive_pct = %s, emp_voice3_neutral_pct = %s, emp_voice3_negative_pct = %s, emp_voice3_score = %s, emp_voice3_priority = %s,
                        emp_voice4_positive_pct = %s, emp_voice4_neutral_pct = %s, emp_voice4_negative_pct = %s, emp_voice4_score = %s, emp_voice4_priority = %s,
                        emp_voice5_positive_pct = %s, emp_voice5_neutral_pct = %s, emp_voice5_negative_pct = %s, emp_voice5_score = %s, emp_voice5_priority = %s,
                        emp_voice6_positive_pct = %s, emp_voice6_neutral_pct = %s, emp_voice6_negative_pct = %s, emp_voice6_score = %s, emp_voice6_priority = %s,
                        emp_voice7_positive_pct = %s, emp_voice7_neutral_pct = %s, emp_voice7_negative_pct = %s, emp_voice7_score = %s, emp_voice7_priority = %s,
                        emp_voice8_positive_pct = %s, emp_voice8_neutral_pct = %s, emp_voice8_negative_pct = %s, emp_voice8_score = %s, emp_voice8_priority = %s,
                        emp_voice9_positive_pct = %s, emp_voice9_neutral_pct = %s, emp_voice9_negative_pct = %s, emp_voice9_score = %s, emp_voice9_priority = %s,
                        emp_voice10_positive_pct = %s, emp_voice10_neutral_pct = %s, emp_voice10_negative_pct = %s, emp_voice10_score = %s, emp_voice10_priority = %s,
                        emp_voice11_positive_pct = %s, emp_voice11_neutral_pct = %s, emp_voice11_negative_pct = %s, emp_voice11_score = %s, emp_voice11_priority = %s,
                        emp_voice12_positive_pct = %s, emp_voice12_neutral_pct = %s, emp_voice12_negative_pct = %s, emp_voice12_score = %s, emp_voice12_priority = %s,
                        emp_voice13_positive_pct = %s, emp_voice13_neutral_pct = %s, emp_voice13_negative_pct = %s, emp_voice13_score = %s, emp_voice13_priority = %s
                        WHERE survey_url = %s
                    ''', (*voice_values, payment_data[11]))

                    # Update strategic alignment gender metrics
                    strategic_alignment_gender_metrics = calculate_strategic_alignment_gender_metrics(survey_responses)
                    strategic_alignment_gender_values = []
                    for key, value in strategic_alignment_gender_metrics.items():
                        strategic_alignment_gender_values.append(value)
                    cursor.execute('''
                        UPDATE companyscore SET
                        strategic_alignment_positive_male_pct = %s, strategic_alignment_neutral_male_pct = %s, 
                        strategic_alignment_negative_male_pct = %s, strategic_alignment_positive_female_pct = %s,
                        strategic_alignment_neutral_female_pct = %s, strategic_alignment_negative_female_pct = %s,
                        strategic_alignment_positive_other_pct = %s, strategic_alignment_neutral_other_pct = %s,
                        strategic_alignment_negative_other_pct = %s  WHERE survey_url = %s
                    ''', (*strategic_alignment_gender_values, payment_data[11]))

                    


                    # Update action metrics
                    communication_metrics = calculate_communication_metrics(survey_responses) 
                    leadership_effectiveness_metrics = calculate_leadership_effectiveness_metrics(survey_responses)
                    work_life_balance_metrics = calculate_work_life_balance_metrics(survey_responses)
                    career_development_metrics = calculate_career_development_metrics(survey_responses)
                    recognition_rewards_metrics = calculate_recognition_rewards_metrics(survey_responses)
                    employee_engagement_metrics = calculate_employee_engagement_metrics(survey_responses)
                    workplace_environment_metrics = calculate_workplace_environment_metrics(survey_responses)
                    inclusion_diversity_metrics = calculate_inclusion_diversity_metrics(survey_responses)
                    compensation_transparency_metrics = calculate_compensation_transparency_metrics(survey_responses)
                    feedback_mechanisms_metrics = calculate_feedback_mechanisms_metrics(survey_responses)
                    organizational_transparency_metrics = calculate_organizational_transparency_metrics(survey_responses)
                    manager_employee_relationship_metrics = calculate_manager_employee_relationship_metrics(survey_responses)
                    psychological_safety_metrics = calculate_psychological_safety_metrics(survey_responses)
                    mission_values_alignment_metrics = calculate_mission_values_alignment_metrics(survey_responses)
                    innovation_creativity_metrics = calculate_innovation_creativity_metrics(survey_responses)

                    cursor.execute('''
                                    INSERT INTO action_table_scores (company_name, survey_url)
                                   VALUES (%s, %s)
                                   ON DUPLICATE KEY UPDATE company_name = VALUES(company_name), survey_url = VALUES(survey_url)
                                   ''', (payment_data[0], payment_data[11]))
                    

                    communication_values = []
                    for key, metrics_tuple in communication_metrics.items():
                        communication_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        communication_positive_pct = %s, communication_neutral_pct = %s, communication_negative_pct = %s, 
                                   communication_score = %s
                        WHERE survey_url = %s
                    ''', (*communication_values, payment_data[11]))

                    leadership_effectiveness_values = []
                    for key, metrics_tuple in leadership_effectiveness_metrics.items():
                        leadership_effectiveness_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        leadership_positive_pct = %s, leadership_neutral_pct = %s, leadership_negative_pct = %s, 
                                   leadership_score = %s
                        WHERE survey_url = %s
                    ''', (*leadership_effectiveness_values, payment_data[11]))

                    work_life_balance_values = []
                    for key, metrics_tuple in work_life_balance_metrics.items():
                        work_life_balance_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        worklife_balance_positive_pct = %s, worklife_balance_neutral_pct = %s, worklife_balance_negative_pct = %s, 
                                   worklife_balance_score = %s
                        WHERE survey_url = %s
                    ''', (*work_life_balance_values, payment_data[11]))
                    career_development_values = []
                    for key, metrics_tuple in career_development_metrics.items():
                        career_development_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        career_development_positive_pct = %s, career_development_neutral_pct = %s, career_development_negative_pct = %s, 
                                   career_development_score = %s
                        WHERE survey_url = %s
                    ''', (*career_development_values, payment_data[11]))
                    recognition_rewards_values = []
                    for key, metrics_tuple in recognition_rewards_metrics.items():
                        recognition_rewards_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        recognition_rewards_positive_pct = %s, recognition_rewards_neutral_pct = %s, recognition_rewards_negative_pct = %s, 
                                   recognition_rewards_score = %s
                        WHERE survey_url = %s
                    ''', (*recognition_rewards_values, payment_data[11]))
                    employee_engagement_values = []
                    for key, metrics_tuple in employee_engagement_metrics.items():
                        employee_engagement_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        employee_engagement_positive_pct = %s, employee_engagement_neutral_pct = %s, employee_engagement_negative_pct = %s, 
                                   employee_engagement_score = %s
                        WHERE survey_url = %s
                    ''', (*employee_engagement_values, payment_data[11]))
                    workplace_environment_values = []
                    for key, metrics_tuple in workplace_environment_metrics.items():
                        workplace_environment_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        workplace_environment_positive_pct = %s, workplace_environment_neutral_pct = %s, workplace_environment_negative_pct = %s, 
                                   workplace_environment_score = %s
                        WHERE survey_url = %s   ''', (*workplace_environment_values, payment_data[11]))
                    inclusion_diversity_values = []
                    for key, metrics_tuple in inclusion_diversity_metrics.items():
                        inclusion_diversity_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        inclusion_diversity_positive_pct = %s, inclusion_diversity_neutral_pct = %s, inclusion_diversity_negative_pct = %s, 
                                   inclusion_diversity_score = %s
                        WHERE survey_url = %s
                    ''', (*inclusion_diversity_values, payment_data[11]))
                    compensation_transparency_values = []
                    for key, metrics_tuple in compensation_transparency_metrics.items():
                        compensation_transparency_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        compensation_transparency_positive_pct = %s, compensation_transparency_neutral_pct = %s, compensation_transparency_negative_pct = %s, 
                                   compensation_transparency_score = %s
                        WHERE survey_url = %s
                    ''', (*compensation_transparency_values, payment_data[11]))
                    feedback_mechanisms_values = []
                    for key, metrics_tuple in feedback_mechanisms_metrics.items():
                        feedback_mechanisms_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        feedback_mechanisms_positive_pct = %s, feedback_mechanisms_neutral_pct = %s, feedback_mechanisms_negative_pct = %s, 
                                   feedback_mechanisms_score = %s
                        WHERE survey_url = %s
                    ''', (*feedback_mechanisms_values, payment_data[11]))
                    organizational_transparency_values = []
                    for key, metrics_tuple in organizational_transparency_metrics.items():
                        organizational_transparency_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        organizational_transparency_positive_pct = %s, organizational_transparency_neutral_pct = %s, organizational_transparency_negative_pct = %s, 
                                   organizational_transparency_score = %s
                        WHERE survey_url = %s
                    ''', (*organizational_transparency_values, payment_data[11]))
                    manager_employee_relationship_values = []
                    for key, metrics_tuple in manager_employee_relationship_metrics.items():
                        manager_employee_relationship_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        manager_employee_relationship_positive_pct = %s, manager_employee_relationship_neutral_pct = %s, manager_employee_relationship_negative_pct = %s, 
                                   manager_employee_relationship_score = %s
                        WHERE survey_url = %s
                    ''', (*manager_employee_relationship_values, payment_data[11]))
                    psychological_safety_values = []
                    for key, metrics_tuple in psychological_safety_metrics.items():
                        psychological_safety_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        psychological_safety_positive_pct = %s, psychological_safety_neutral_pct = %s, psychological_safety_negative_pct = %s, 
                                   psychological_safety_score = %s
                        WHERE survey_url = %s
                    ''', (*psychological_safety_values, payment_data[11]))
                    mission_values_alignment_values = []
                    for key, metrics_tuple in mission_values_alignment_metrics.items():
                        mission_values_alignment_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        mission_values_alignment_positive_pct = %s, mission_values_alignment_neutral_pct = %s, mission_values_alignment_negative_pct = %s, 
                                   mission_values_alignment_score = %s
                        WHERE survey_url = %s
                    ''', (*mission_values_alignment_values, payment_data[11]))
                    innovation_creativity_values = []
                    for key, metrics_tuple in innovation_creativity_metrics.items():
                        innovation_creativity_values.append(metrics_tuple)
                    cursor.execute('''
                        UPDATE action_table_scores SET
                        innovation_creativity_positive_pct = %s, innovation_creativity_neutral_pct = %s, innovation_creativity_negative_pct = %s, 
                                   innovation_creativity_score = %s
                        WHERE survey_url = %s
                    ''', (*innovation_creativity_values, payment_data[11]))



                    # Make sure all changes are committed
                    db.commit()

                    # Make sure all cursors are closed
                    cursor.close()

                    # Close the database connection
                    db.close()

                    # Clear the session and redirect
                    session.clear()
                    return redirect(url_for('thank_you'))

                return redirect(f"/survey/{email_id}")

    if 'step' not in session:
        return render_template('quiz.html', stage='user', company=session.get('company_name'))
    else:
        qnum = session['step']
        current_q = {
            "q": QUESTIONS[qnum],
            "options": OPTIONS[qnum]
        }
        # Get previously selected answer if available
        selected_answer = None
        if qnum < len(session.get('answers', [])):
            selected_answer = session['answers'][qnum][1]
        return render_template('quiz.html', stage='question', question=current_q,
                             qnum=qnum + 1, company=session.get('company_name'),
                             QUESTIONS=QUESTIONS, selected_answer=selected_answer)

@app.route('/free-trial/<email_id>', methods=['GET', 'POST'])
def free_trial_quiz(email_id):
    """Free trial route for 6 questions only [1,2,5,16,10,15]"""
    if '@' not in email_id or '.' not in email_id:
        return """
            <script>
                alert("❌ Invalid URL format. Expected email in the URL.");
                window.location.href = "/";
            </script>
        """, 400

    # Extract company from email
    company_name = email_id.split('@')[-1].split('.')[0]
    session['email_from_url'] = email_id
    session['company_name'] = company_name.capitalize()

    if request.method == 'POST':
        if 'step' not in session:
            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['step'] = 0
            session['answers'] = []
            return redirect(f"/free-trial/{email_id}")
        else:
            if 'back' in request.form:
                if session['step'] > 0:
                    session['step'] -= 1
                return redirect(f"/free-trial/{email_id}")
            else:
                selected_value = request.form.get('option')
                if not selected_value:
                    qnum = session['step']

                    # Safety check: ensure qnum is within bounds
                    if qnum >= len(FREE_QUESTIONS):
                        session.clear()  # Clear corrupted session
                        return redirect(f"/free-trial/{email_id}")

                    current_q = {
                        "q": FREE_QUESTIONS[qnum],
                        "options": FREE_OPTIONS[qnum]
                    }
                    return render_template('free_quiz.html', stage='question', question=current_q,
                                         qnum=qnum + 1, company=session.get('company_name'),
                                         QUESTIONS=FREE_QUESTIONS, error="Please select an option")

                selected_option = selected_value[0]
                # Update or append the answer
                if session['step'] < len(session['answers']):
                    session['answers'][session['step']] = (selected_option, selected_value)
                else:
                    session['answers'].append((selected_option, selected_value))
                session['step'] += 1

                if session['step'] >= len(FREE_QUESTIONS):
                    db = get_db()
                    cursor = db.cursor()
                    form_url = request.url  # Free trial URL

                    # Generate random unique_id for better clarity to identify single person responses
                    import random
                    session['unique_id'] = str(random.randint(100000, 999999))  # 6-digit random unique ID

                    # Process answers for free trial questions only
                    for i, (_, text) in enumerate(session['answers']):
                        if i < len(FREE_QUESTIONS):
                            # Get sentiment analysis
                            sentiment = predict_sentiment(refine_sentence(FREE_QUESTIONS[i], text))
                            # Map to original question number
                            original_question_number = FREE_TO_ORIGINAL_MAPPING[i]

                            cursor.execute(
                                "INSERT INTO student_data (unique_id, gender, age_group, tenure_group, role, department, question_number, question_text, selected_text, form_url, predicted_sentiment) "
                                "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)",
                                (session['unique_id'], session['gender'],
                                 session['age_group'], session['tenure_group'], session['role'],
                                 session['department'], original_question_number, FREE_QUESTIONS[i], text, form_url, sentiment)
                            )

                    # Commit only once after all inserts
                    db.commit()
                    cursor.close()
                    db.close()
                    session.clear()
                    return redirect(url_for('free_thank_you'))

                return redirect(f"/free-trial/{email_id}")

    if 'step' not in session:
        return render_template('free_quiz.html', stage='user', company=session.get('company_name'))
    else:
        qnum = session['step']

        # Safety check: ensure qnum is within bounds
        if qnum >= len(FREE_QUESTIONS):
            session.clear()  # Clear corrupted session
            return redirect(f"/free-trial/{email_id}")

        current_q = {
            "q": FREE_QUESTIONS[qnum],
            "options": FREE_OPTIONS[qnum]
        }
        selected_answer = None
        if qnum < len(session.get('answers', [])):
            selected_answer = session['answers'][qnum][1]
        return render_template('free_quiz.html', stage='question', question=current_q,
                             qnum=qnum + 1, company=session.get('company_name'),
                             QUESTIONS=FREE_QUESTIONS, selected_answer=selected_answer)

@app.route('/thankyou')
def thank_you():
    return render_template('thank_you.html')

@app.route('/free-thankyou')
def free_thank_you():
    return render_template('free_thank-you.html')

@app.route('/clear-session')
def clear_session():
    session.clear()
    return "Session cleared! You can now start fresh."


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8000)
