"""
Action Table Metrics Calculator - Free Version

This module provides functions to calculate metrics for various action categories
from survey responses, including scores and sentiment percentages.
Only considers questions [1, 2, 5, 16, 10, 15] for free version.
"""

from decimal import Decimal

# Question Category Mappings - Free Version (only questions [1, 2, 5, 16, 10, 15])
COMMUNICATION_QUESTIONS = []  # No questions from free set
LEADERSHIP_EFFECTIVENESS_QUESTIONS = [1, 5, 15]  # From [1, 3, 4, 5, 6, 8, 9, 11, 12, 13, 15]
WORK_LIFE_BALANCE_QUESTIONS = []  # No questions from free set
CAREER_DEVELOPMENT_QUESTIONS = [1]  # From [1, 4, 6]
RECOGNITION_REWARDS_QUESTIONS = [10]  # From [10]
EMPLOYEE_ENGAGEMENT_QUESTIONS = [2, 10, 15]  # From [2, 3, 8, 9, 10, 15, 19]
WORKPLACE_ENVIRONMENT_QUESTIONS = [1, 2, 5]  # From [1, 2, 3, 5, 8, 9, 12, 13, 17, 18, 20]
INCLUSION_DIVERSITY_QUESTIONS = [1, 5, 16]  # From [1, 3, 5, 12, 14, 16, 18, 19]
COMPENSATION_TRANSPARENCY_QUESTIONS = [16]  # From [16]
FEEDBACK_MECHANISMS_QUESTIONS = []  # No questions from free set
ORGANIZATIONAL_TRANSPARENCY_QUESTIONS = [1, 15, 16]  # From [1, 14, 15, 16, 19]
MANAGER_EMPLOYEE_RELATIONSHIP_QUESTIONS = [5]  # From [5, 6, 8, 13, 17]
PSYCHOLOGICAL_SAFETY_QUESTIONS = [2, 10]  # From [2, 3, 9, 10, 12]
MISSION_VALUES_ALIGNMENT_QUESTIONS = []  # No questions from free set
INNOVATION_CREATIVITY_QUESTIONS = []  # No questions from free set

def calculate_sentiment_percentages(survey_responses):
    """Calculate overall sentiment percentages."""
    total_with_sentiment = survey_responses[survey_responses['predicted_sentiment'].notna()].shape[0]  
    
    if total_with_sentiment == 0:
        return Decimal('0.00'), Decimal('0.00'), Decimal('0.00')

    positive_count = survey_responses[survey_responses['predicted_sentiment'] == "Positive"].shape[0]  
    neutral_count = survey_responses[survey_responses['predicted_sentiment'] == "Neutral"].shape[0]  
    negative_count = survey_responses[survey_responses['predicted_sentiment'] == "Negative"].shape[0] 

    positive_pct = (Decimal(positive_count) / Decimal(total_with_sentiment)) * 100
    neutral_pct = (Decimal(neutral_count) / Decimal(total_with_sentiment)) * 100
    negative_pct = (Decimal(negative_count) / Decimal(total_with_sentiment)) * 100

    return (
        positive_pct.quantize(Decimal('0.01')),
        neutral_pct.quantize(Decimal('0.01')),
        negative_pct.quantize(Decimal('0.01'))
    )

def calculate_category_sentiment_percentages(survey_responses, question_numbers):
    """Calculate sentiment percentages for a specific category of questions."""
    category_responses = survey_responses[survey_responses['question_number'].isin(question_numbers)]
    return calculate_sentiment_percentages(category_responses)

def calculate_action_score(positive_pct, neutral_pct, negative_pct):
    """Calculate action score based on sentiment percentages using same weights as DEI score."""
    score = (
        (positive_pct * Decimal('1.0')) +
        (neutral_pct * Decimal('0.5')) +
        (negative_pct * Decimal('-0.1'))
    )
    return score.quantize(Decimal('0.01'))

def calculate_communication_metrics(survey_responses):
    """Calculate communication metrics and return them in a dictionary."""
    if not COMMUNICATION_QUESTIONS:
        return {
            'positive_pct': Decimal('0.00'),
            'neutral_pct': Decimal('0.00'),
            'negative_pct': Decimal('0.00'),
            'score': Decimal('0.00')
        }
    
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, COMMUNICATION_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_leadership_effectiveness_metrics(survey_responses):
    """Calculate leadership effectiveness metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, LEADERSHIP_EFFECTIVENESS_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_work_life_balance_metrics(survey_responses):
    """Calculate work-life balance metrics and return them in a dictionary."""
    if not WORK_LIFE_BALANCE_QUESTIONS:
        return {
            'positive_pct': Decimal('0.00'),
            'neutral_pct': Decimal('0.00'),
            'negative_pct': Decimal('0.00'),
            'score': Decimal('0.00')
        }
    
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, WORK_LIFE_BALANCE_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_career_development_metrics(survey_responses):
    """Calculate career development metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, CAREER_DEVELOPMENT_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_recognition_rewards_metrics(survey_responses):
    """Calculate recognition and rewards metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, RECOGNITION_REWARDS_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_employee_engagement_metrics(survey_responses):
    """Calculate employee engagement metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, EMPLOYEE_ENGAGEMENT_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_workplace_environment_metrics(survey_responses):
    """Calculate workplace environment metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, WORKPLACE_ENVIRONMENT_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_inclusion_diversity_metrics(survey_responses):
    """Calculate inclusion and diversity metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, INCLUSION_DIVERSITY_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_compensation_transparency_metrics(survey_responses):
    """Calculate compensation transparency metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, COMPENSATION_TRANSPARENCY_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_feedback_mechanisms_metrics(survey_responses):
    """Calculate feedback mechanisms metrics and return them in a dictionary."""
    if not FEEDBACK_MECHANISMS_QUESTIONS:
        return {
            'positive_pct': Decimal('0.00'),
            'neutral_pct': Decimal('0.00'),
            'negative_pct': Decimal('0.00'),
            'score': Decimal('0.00')
        }
    
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, FEEDBACK_MECHANISMS_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_organizational_transparency_metrics(survey_responses):
    """Calculate organizational transparency metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, ORGANIZATIONAL_TRANSPARENCY_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_manager_employee_relationship_metrics(survey_responses):
    """Calculate manager-employee relationship metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, MANAGER_EMPLOYEE_RELATIONSHIP_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }

def calculate_psychological_safety_metrics(survey_responses):
    """Calculate psychological safety metrics and return them in a dictionary."""
    positive_pct, neutral_pct, negative_pct = calculate_category_sentiment_percentages(
        survey_responses, PSYCHOLOGICAL_SAFETY_QUESTIONS
    )
    score = calculate_action_score(positive_pct, neutral_pct, negative_pct)
    
    return {
        'positive_pct': positive_pct,
        'neutral_pct': neutral_pct,
        'negative_pct': negative_pct,
        'score': score
    }
